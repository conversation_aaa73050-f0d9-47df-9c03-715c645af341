import { IncidentTicket } from '@/types';

/**
 * LLM Evaluation Response Interface
 * Based on MVP requirements for quality evaluation
 */
export interface LLMEvaluationResponse {
  ranking: 'Poor' | 'Below Average' | 'Average' | 'Good' | 'Excellent' | 'Error';
  reviewComments: string;
}

/**
 * LLM Service Configuration
 */
interface LLMConfig {
  apiUrl: string;
  apiKey: string;
  timeout?: number;
  maxRetries?: number;
}

/**
 * LLM Service for evaluating incident tickets
 */
export class LLMService {
  private baseURL: string;
  private apiKey: string;
  private timeout: number;
  private maxRetries: number;

  constructor(config: LLMConfig) {
    this.baseURL = config.apiUrl;
    this.apiKey = config.apiKey;
    this.timeout = config.timeout || 30000;
    this.maxRetries = config.maxRetries || 3;
  }

  /**
   * Evaluate a single incident ticket using LLM
   */
  async evaluateTicket(ticket: IncidentTicket): Promise<LLMEvaluationResponse> {
    const prompt = this.buildEvaluationPrompt(ticket);
    
    for (let attempt = 1; attempt <= this.maxRetries; attempt++) {
      try {
        // Use the configured model or default
        const model = process.env.LLM_MODEL || 'gpt-3.5-turbo';

        const response = await fetch(this.baseURL, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${this.apiKey}`
          },
          body: JSON.stringify({
            model: model,
            messages: [{
              role: 'user',
              content: prompt
            }],
            temperature: 0.1,
            max_tokens: 500,
            response_format: { type: 'json_object' }
          }),
          signal: AbortSignal.timeout(this.timeout)
        });

        if (!response.ok) {
          const errorText = await response.text().catch(() => 'Unknown error');
          throw new Error(`HTTP ${response.status}: ${response.statusText}. Response: ${errorText}`);
        }

        const data = await response.json();
        const content = data.choices[0]?.message?.content;
        
        if (!content) {
          throw new Error('No content in LLM response');
        }

        const evaluation = JSON.parse(content);
        return {
          ranking: evaluation.ranking || 'Average',
          reviewComments: evaluation.reviewComments || 'No specific feedback provided.'
        };

      } catch (error) {
        console.error(`LLM evaluation attempt ${attempt} failed:`, error);
        
        if (attempt === this.maxRetries) {
          // Return fallback evaluation on final failure
          return this.getFallbackEvaluation(ticket);
        }
        
        // Wait before retry (exponential backoff)
        await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt) * 1000));
      }
    }

    // This should never be reached, but TypeScript requires it
    return this.getFallbackEvaluation(ticket);
  }

  /**
   * Build evaluation prompt for the LLM
   */
  private buildEvaluationPrompt(ticket: IncidentTicket): string {
    return `
You are an expert incident ticket quality evaluator focused on business stakeholder accessibility. Your role is to evaluate how well this incident ticket communicates to NON-TECHNICAL audiences including business stakeholders and senior management who lack deep technical knowledge.

Incident Details:
- Service: ${ticket.Service}
- Problem Service: ${ticket.ProblemService}
- Summary: ${ticket.Summary}
- Business Impact: ${ticket.BusinessImpact}
- Technical Details: ${ticket.TechnicalDetails}
- Instructions: ${ticket.Instructions}

Evaluate this ticket from the perspective of a BUSINESS STAKEHOLDER or SENIOR MANAGER who needs to understand:
1. **Business Impact Clarity**: Can a non-technical person understand what's broken and how it affects business operations?
2. **Plain English Communication**: Are technical terms explained or avoided? Is jargon translated into business language?
3. **Executive Summary Quality**: Can senior management quickly grasp the situation, urgency, and business implications?
4. **Actionable Information**: Are next steps, timelines, and expectations understandable to business stakeholders?

Focus on accessibility for business audiences, NOT technical accuracy. Evaluate whether:
- A CIO could understand the business impact from reading this ticket
- Technical jargon is explained in plain English
- The update and instructions are clear to non-technical management

Please respond with a JSON object containing:
{
  "ranking": "one of: Poor, Below Average, Average, Good, Excellent",
  "reviewComments": "detailed feedback focused on improving business stakeholder accessibility and plain English communication"
}

Provide specific suggestions for translating technical information into business-friendly language.
    `.trim();
  }

  /**
   * Provide fallback evaluation when LLM fails
   * Focuses on business stakeholder accessibility
   */
  private getFallbackEvaluation(ticket: IncidentTicket): LLMEvaluationResponse {
    let ranking: 'Poor' | 'Below Average' | 'Average' | 'Good' | 'Excellent' = 'Average';
    let reviewComments = 'Business Accessibility Assessment: ';

    // Evaluate business stakeholder accessibility
    const businessImpactClarity = this.assessBusinessImpactClarity(ticket.BusinessImpact);
    const plainEnglishUsage = this.assessPlainEnglishUsage(ticket);
    const executiveSummaryQuality = this.assessExecutiveSummaryQuality(ticket.Summary);
    const urgencyClarity = this.assessUrgencyClarity(ticket.BusinessImpact);

    const accessibilityScores = [businessImpactClarity, plainEnglishUsage, executiveSummaryQuality, urgencyClarity];
    const averageScore = accessibilityScores.reduce((sum, score) => sum + score, 0) / accessibilityScores.length;

    // Determine ranking based on business accessibility
    if (averageScore >= 0.8) {
      ranking = 'Excellent';
      reviewComments += 'This ticket effectively communicates to business stakeholders. ';
    } else if (averageScore >= 0.6) {
      ranking = 'Good';
      reviewComments += 'This ticket is generally accessible to business users with minor improvements needed. ';
    } else if (averageScore >= 0.4) {
      ranking = 'Average';
      reviewComments += 'This ticket needs improvement to be accessible to non-technical stakeholders. ';
    } else if (averageScore >= 0.2) {
      ranking = 'Below Average';
      reviewComments += 'This ticket is difficult for business stakeholders to understand. ';
    } else {
      ranking = 'Poor';
      reviewComments += 'This ticket is not accessible to business stakeholders and needs significant improvement. ';
    }

    // Add specific business-focused suggestions
    if (businessImpactClarity < 0.5) {
      reviewComments += 'Clarify the business impact in terms that executives and business users can understand. ';
    }
    if (plainEnglishUsage < 0.5) {
      reviewComments += 'Replace technical jargon with plain English explanations that business stakeholders can follow. ';
    }
    if (executiveSummaryQuality < 0.5) {
      reviewComments += 'Provide a clear executive summary that senior management can quickly understand. ';
    }
    if (urgencyClarity < 0.5) {
      reviewComments += 'Make the business priority and urgency clear to non-technical decision makers. ';
    }

    return { ranking, reviewComments };
  }

  /**
   * Assess how clearly the business impact is communicated to non-technical stakeholders
   */
  private assessBusinessImpactClarity(businessImpact: string): number {
    if (!businessImpact || businessImpact.length < 10) return 0.1;

    const impact = businessImpact.toLowerCase();
    let score = 0.3; // Base score for having content

    // Check for business-friendly language
    if (impact.includes('customer') || impact.includes('user') || impact.includes('revenue')) score += 0.2;
    if (impact.includes('critical') || impact.includes('urgent') || impact.includes('high priority')) score += 0.2;
    if (impact.includes('service') && (impact.includes('down') || impact.includes('unavailable'))) score += 0.2;

    // Penalize for excessive technical jargon
    const technicalTerms = ['api', 'database', 'server', 'network', 'ssl', 'dns', 'tcp', 'http'];
    const jargonCount = technicalTerms.filter(term => impact.includes(term)).length;
    if (jargonCount > 2) score -= 0.2;

    return Math.min(Math.max(score, 0), 1);
  }

  /**
   * Assess how well the ticket uses plain English vs technical jargon
   */
  private assessPlainEnglishUsage(ticket: IncidentTicket): number {
    const allText = `${ticket.Summary} ${ticket.BusinessImpact} ${ticket.Instructions}`.toLowerCase();
    if (allText.length < 20) return 0.1;

    let score = 0.5; // Base score

    // Check for excessive technical jargon
    const technicalJargon = [
      'api', 'ssl', 'tcp', 'http', 'dns', 'cpu', 'ram', 'sql', 'json', 'xml',
      'oauth', 'saml', 'ldap', 'vpn', 'firewall', 'load balancer', 'microservice'
    ];
    const jargonCount = technicalJargon.filter(term => allText.includes(term)).length;

    // Penalize for jargon without explanation
    if (jargonCount > 3) score -= 0.3;
    else if (jargonCount > 1) score -= 0.1;

    // Reward business-friendly explanations
    if (allText.includes('which means') || allText.includes('in other words') || allText.includes('this affects')) {
      score += 0.2;
    }

    return Math.min(Math.max(score, 0), 1);
  }

  /**
   * Assess the quality of executive summary information
   */
  private assessExecutiveSummaryQuality(summary: string): number {
    if (!summary || summary.length < 15) return 0.1;

    const summaryLower = summary.toLowerCase();
    let score = 0.3; // Base score for having content

    // Check for executive-friendly elements
    if (summaryLower.includes('impact') || summaryLower.includes('affect')) score += 0.2;
    if (summaryLower.includes('customer') || summaryLower.includes('business')) score += 0.2;
    if (summary.length > 50 && summary.length < 200) score += 0.2; // Good length for executives

    // Penalize for being too technical or too brief
    if (summary.length < 30) score -= 0.2;
    if (summaryLower.split(' ').length < 5) score -= 0.2;

    return Math.min(Math.max(score, 0), 1);
  }

  /**
   * Assess how clearly urgency is communicated to business decision makers
   */
  private assessUrgencyClarity(businessImpact: string): number {
    if (!businessImpact || businessImpact.length < 10) return 0.1;

    const impact = businessImpact.toLowerCase();
    let score = 0.3; // Base score

    // Check for clear urgency indicators
    if (impact.includes('critical') || impact.includes('urgent') || impact.includes('immediate')) score += 0.3;
    if (impact.includes('high') || impact.includes('priority') || impact.includes('escalate')) score += 0.2;
    if (impact.includes('customer') && (impact.includes('unable') || impact.includes('cannot'))) score += 0.2;

    return Math.min(Math.max(score, 0), 1);
  }
}

/**
 * Mock LLM Service for development/testing
 * Focuses on business stakeholder accessibility evaluation
 */
export class MockLLMService {
  async evaluateTicket(ticket: IncidentTicket): Promise<LLMEvaluationResponse> {
    // Simulate processing delay
    await new Promise(resolve => setTimeout(resolve, 500 + Math.random() * 1000));

    // Business accessibility evaluation
    const businessAccessibility = this.evaluateBusinessAccessibility(ticket);
    const plainEnglishUsage = this.evaluatePlainEnglishUsage(ticket);
    const executiveClarity = this.evaluateExecutiveClarity(ticket);
    const urgencyClarity = this.evaluateUrgencyClarity(ticket);

    // Determine overall ranking based on business stakeholder accessibility
    const accessibilityScores = [businessAccessibility, plainEnglishUsage, executiveClarity, urgencyClarity];
    const averageScore = accessibilityScores.reduce((sum, score) => sum + score, 0) / accessibilityScores.length;

    let ranking: 'Poor' | 'Below Average' | 'Average' | 'Good' | 'Excellent';
    let baseComments: string;

    if (averageScore >= 0.8) {
      ranking = 'Excellent';
      baseComments = 'This ticket effectively communicates to business stakeholders and senior management.';
    } else if (averageScore >= 0.6) {
      ranking = 'Good';
      baseComments = 'This ticket is generally accessible to business users with minor improvements needed.';
    } else if (averageScore >= 0.4) {
      ranking = 'Average';
      baseComments = 'This ticket needs improvement to be fully accessible to non-technical stakeholders.';
    } else if (averageScore >= 0.2) {
      ranking = 'Below Average';
      baseComments = 'This ticket is difficult for business stakeholders to understand and requires significant improvement.';
    } else {
      ranking = 'Poor';
      baseComments = 'This ticket is not accessible to business stakeholders and needs major revision.';
    }

    // Generate business-focused feedback
    const suggestions = this.generateBusinessFocusedSuggestions(ticket, {
      businessAccessibility,
      plainEnglishUsage,
      executiveClarity,
      urgencyClarity
    });

    const reviewComments = `${baseComments} ${suggestions.join(' ')}`;

    return {
      ranking,
      reviewComments
    };
  }

  /**
   * Evaluate business accessibility of the ticket
   */
  private evaluateBusinessAccessibility(ticket: IncidentTicket): number {
    const businessImpact = ticket.BusinessImpact?.toLowerCase() || '';
    if (businessImpact.length < 10) return 0.2;

    let score = 0.4;

    // Reward business-friendly language
    if (businessImpact.includes('customer') || businessImpact.includes('user')) score += 0.2;
    if (businessImpact.includes('revenue') || businessImpact.includes('sales')) score += 0.2;
    if (businessImpact.includes('service') && businessImpact.includes('unavailable')) score += 0.2;

    return Math.min(score, 1);
  }

  /**
   * Evaluate plain English usage vs technical jargon
   */
  private evaluatePlainEnglishUsage(ticket: IncidentTicket): number {
    const allText = `${ticket.Summary} ${ticket.BusinessImpact} ${ticket.Instructions}`.toLowerCase();
    if (allText.length < 20) return 0.2;

    let score = 0.6;

    // Penalize technical jargon
    const jargon = ['api', 'ssl', 'tcp', 'dns', 'cpu', 'sql', 'json'];
    const jargonCount = jargon.filter(term => allText.includes(term)).length;
    if (jargonCount > 2) score -= 0.3;

    // Reward explanatory language
    if (allText.includes('which means') || allText.includes('this affects')) score += 0.2;

    return Math.max(score, 0.1);
  }

  /**
   * Evaluate executive-level clarity
   */
  private evaluateExecutiveClarity(ticket: IncidentTicket): number {
    const summary = ticket.Summary || '';
    if (summary.length < 15) return 0.2;

    let score = 0.4;

    if (summary.toLowerCase().includes('impact') || summary.toLowerCase().includes('affect')) score += 0.2;
    if (summary.length > 30 && summary.length < 150) score += 0.2; // Good executive length
    if (summary.toLowerCase().includes('business') || summary.toLowerCase().includes('customer')) score += 0.2;

    return Math.min(score, 1);
  }

  /**
   * Evaluate urgency clarity for business decision makers
   */
  private evaluateUrgencyClarity(ticket: IncidentTicket): number {
    const businessImpact = ticket.BusinessImpact?.toLowerCase() || '';
    if (businessImpact.length < 10) return 0.2;

    let score = 0.4;

    if (businessImpact.includes('critical') || businessImpact.includes('urgent')) score += 0.3;
    if (businessImpact.includes('high priority') || businessImpact.includes('immediate')) score += 0.2;
    if (businessImpact.includes('customer') && businessImpact.includes('unable')) score += 0.1;

    return Math.min(score, 1);
  }

  /**
   * Generate business-focused improvement suggestions
   */
  private generateBusinessFocusedSuggestions(ticket: IncidentTicket, scores: {
    businessAccessibility: number;
    plainEnglishUsage: number;
    executiveClarity: number;
    urgencyClarity: number;
  }): string[] {
    const suggestions: string[] = [];

    if (scores.businessAccessibility < 0.6) {
      suggestions.push('Explain the business impact in terms that executives and business stakeholders can understand.');
    }

    if (scores.plainEnglishUsage < 0.6) {
      suggestions.push('Replace technical jargon with plain English explanations that non-technical staff can follow.');
    }

    if (scores.executiveClarity < 0.6) {
      suggestions.push('Provide a clear executive summary that senior management can quickly grasp.');
    }

    if (scores.urgencyClarity < 0.6) {
      suggestions.push('Make the business priority and timeline clear to non-technical decision makers.');
    }

    if (suggestions.length === 0) {
      suggestions.push('Consider adding more context about how this affects business operations and what stakeholders should expect.');
    }

    return suggestions;
  }
}

/**
 * Create LLM service instance with environment configuration
 */
export function createLLMService(): LLMService | MockLLMService {
  const apiUrl = process.env.LLM_API_URL;
  const apiKey = process.env.LLM_API_KEY;
  const useMock = process.env.USE_MOCK_LLM === 'true';

  // Use mock service if explicitly requested or if credentials are missing
  if (useMock || !apiUrl || !apiKey) {
    console.log('[LLM SERVICE] Using mock LLM service for development');
    return new MockLLMService();
  }

  console.log(`[LLM SERVICE] Using real LLM service at ${apiUrl}`);
  return new LLMService({
    apiUrl,
    apiKey,
    timeout: parseInt(process.env.LLM_TIMEOUT || '30000'),
    maxRetries: parseInt(process.env.LLM_MAX_RETRIES || '3')
  });
}

/**
 * Team assignment mapping based on category
 */
export const TEAM_ASSIGNMENTS: Record<string, string> = {
  'Network': 'Infrastructure Team',
  'Hardware': 'Infrastructure Team',
  'Software': 'Development Team',
  'Security': 'Security Team',
  'Application': 'Development Team'
};
